import React, { useContext } from 'react';
import './Header.css';
import logo from './assets/PrimeIcon.jpeg';
import { Link } from 'react-router-dom';
import { logout } from './services/authentication.service';
import { useNavigate } from 'react-router-dom';
import { signOut } from './services/authentication.service';
import globalContext from './GlobalContext';

const Header = () => {
  const GlobalContext = useContext(globalContext);
  let navigate = useNavigate();
  const appLogout = () => {
    try {
      GlobalContext.startSpinner();
      signOut().then((resp) => {
      }).catch((error) => {
        console.log('Failed to logout: ' + error);
      });
    } catch (er) {
      console.error('Error loging out: ', er);
    } finally {
      GlobalContext.stopSpinner();
      logout();
      navigate('/');
    }
  };
  return (
    <header className="header bg-white">
      <nav className="navbar navbar-expand-md navbar-light bg-white fixed-top border-bottom border-color-primary py-2">
        <div className="container container-fluid">
          <div className="header-flex w-100 d-flex align-items-center justify-content-between">
            <Link to="/facilities" className="navbar-brand d-flex align-items-center">
              <div className="logo">
                <img src={logo} alt="logo" className="me-1" />
                <span className="custom-color-maroon">Scan</span><strong className="custom-bg-blue text-white">hub</strong>
              </div>
            </Link>
            <button className="navbar-toggler border-0 p-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse"
              aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation"
              style={{
                position: 'fixed',
                right: '15px',
                top: '15px',
                transform: 'translateZ(0)',
                WebkitTransform: 'translateZ(0)',
                zIndex: 1031,
                margin: 0,
                WebkitTapHighlightColor: 'transparent',
                WebkitTouchCallout: 'none',
                WebkitUserSelect: 'none',
                userSelect: 'none'
              }}>
              <span className="navbar-toggler-icon"></span>
            </button>
          </div>
          <div className="collapse navbar-collapse" id="navbarCollapse" style={{
            position: 'fixed',
            top: '60px',
            right: '15px',
            zIndex: 1030,
            backgroundColor: 'white',
            border: '1px solid #dee2e6',
            borderRadius: '0.375rem',
            boxShadow: '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)',
            minWidth: '120px'
          }}>
            <ul className="navbar-nav mb-0">
              <li className="nav-item">
                <a onClick={appLogout} className="nav-link px-3 py-2 link-dark custom-color-blue font-size-small fw-bold text-shadow active" style={{cursor: 'pointer'}}>Logout</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </header>
  );
};
export default Header;